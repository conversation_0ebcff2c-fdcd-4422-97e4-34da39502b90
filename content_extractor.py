#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容提取器
从现有.docx文件中提取有用内容，用于标准撰写参考
"""

from docx import Document
import re
import json
from pathlib import Path

class ContentExtractor:
    def __init__(self):
        self.extracted_content = {}
        
    def extract_from_file(self, file_path, content_type="general"):
        """从指定文件提取内容"""
        try:
            doc = Document(file_path)
            file_name = Path(file_path).name
            
            content = {
                'file_name': file_name,
                'content_type': content_type,
                'sections': [],
                'tables': [],
                'key_phrases': [],
                'technical_terms': [],
                'requirements': [],
                'standards_references': []
            }
            
            # 提取段落内容
            current_section = None
            for para in doc.paragraphs:
                text = para.text.strip()
                if not text:
                    continue
                    
                style = para.style.name if para.style else 'Normal'
                
                # 识别章节标题
                if self._is_section_title(text, style):
                    current_section = {
                        'title': text,
                        'style': style,
                        'content': []
                    }
                    content['sections'].append(current_section)
                elif current_section:
                    current_section['content'].append(text)
                
                # 提取关键短语
                key_phrases = self._extract_key_phrases(text)
                content['key_phrases'].extend(key_phrases)
                
                # 提取技术术语
                tech_terms = self._extract_technical_terms(text)
                content['technical_terms'].extend(tech_terms)
                
                # 提取要求性语句
                requirements = self._extract_requirements(text)
                content['requirements'].extend(requirements)
                
                # 提取标准引用
                std_refs = self._extract_standard_references(text)
                content['standards_references'].extend(std_refs)
            
            # 提取表格内容
            for table_idx, table in enumerate(doc.tables):
                table_data = {
                    'index': table_idx,
                    'rows': len(table.rows),
                    'cols': len(table.columns),
                    'content': []
                }
                
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_data.append(cell_text)
                    if row_data:
                        table_data['content'].append(row_data)
                
                content['tables'].append(table_data)
            
            # 去重
            content['key_phrases'] = list(set(content['key_phrases']))
            content['technical_terms'] = list(set(content['technical_terms']))
            content['requirements'] = list(set(content['requirements']))
            content['standards_references'] = list(set(content['standards_references']))
            
            self.extracted_content[file_name] = content
            return content
            
        except Exception as e:
            print(f"提取文件 {file_path} 内容时出错: {e}")
            return None
    
    def _is_section_title(self, text, style):
        """判断是否为章节标题"""
        title_indicators = [
            '章标题', '目次', '前言', '引言', 'Heading',
            '一级条标题', '二级条标题'
        ]
        
        if any(indicator in style for indicator in title_indicators):
            return True
        
        # 基于文本模式判断
        patterns = [
            r'^\d+\s+\w+',  # 如 "1 范围"
            r'^\d+\.\d+\s+\w+',  # 如 "1.1 总则"
            r'^第\d+章',  # 如 "第1章"
            r'^附录[A-Z]',  # 如 "附录A"
        ]
        
        return any(re.match(pattern, text) for pattern in patterns)
    
    def _extract_key_phrases(self, text):
        """提取关键短语"""
        key_phrases = []
        
        # 技术相关关键词
        tech_keywords = [
            '智能检测', '视觉缺陷', '图像处理', '深度学习', '神经网络',
            '目标检测', '图像分割', '特征提取', '模式识别', '机器学习',
            '变电设备', '电力设备', '缺陷识别', '故障诊断', '状态监测'
        ]
        
        for keyword in tech_keywords:
            if keyword in text:
                key_phrases.append(keyword)
        
        return key_phrases
    
    def _extract_technical_terms(self, text):
        """提取技术术语"""
        terms = []
        
        # 匹配英文术语定义模式
        english_pattern = r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*[：:]?\s*([a-zA-Z\s]+)'
        matches = re.findall(english_pattern, text)
        
        for match in matches:
            if len(match[1].split()) <= 5:  # 限制英文术语长度
                terms.append(f"{match[0]} - {match[1]}")
        
        # 匹配术语编号模式
        term_pattern = r'(\d+\.\d+)\s+([^：:]+)[：:]?\s*([a-zA-Z\s]+)?'
        matches = re.findall(term_pattern, text)
        
        for match in matches:
            if len(match[1]) < 50:  # 限制术语长度
                term_entry = f"{match[0]} {match[1]}"
                if match[2]:
                    term_entry += f" - {match[2]}"
                terms.append(term_entry)
        
        return terms
    
    def _extract_requirements(self, text):
        """提取要求性语句"""
        requirements = []
        
        # 要求性关键词
        requirement_keywords = ['应', '必须', '应当', '需要', '要求', '规定', '不得', '禁止']
        
        if any(keyword in text for keyword in requirement_keywords):
            # 分句处理
            sentences = re.split(r'[。；;]', text)
            for sentence in sentences:
                sentence = sentence.strip()
                if sentence and any(keyword in sentence for keyword in requirement_keywords):
                    if len(sentence) < 200:  # 限制长度
                        requirements.append(sentence)
        
        return requirements
    
    def _extract_standard_references(self, text):
        """提取标准引用"""
        references = []
        
        # 标准引用模式
        patterns = [
            r'GB/T\s+\d+(?:\.\d+)?-\d{4}',  # GB/T标准
            r'GB\s+\d+(?:\.\d+)?-\d{4}',    # GB标准
            r'DL/T\s+\d+(?:\.\d+)?-\d{4}',  # DL/T标准
            r'IEEE\s+\d+(?:\.\d+)?',        # IEEE标准
            r'ISO/IEC\s+\d+(?:\.\d+)?',     # ISO/IEC标准
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            references.extend(matches)
        
        return references
    
    def generate_content_summary(self):
        """生成内容摘要"""
        summary = {
            'total_files': len(self.extracted_content),
            'files_overview': {},
            'common_terms': {},
            'common_requirements': {},
            'all_standards': []
        }
        
        all_terms = []
        all_requirements = []
        all_standards = []
        
        for file_name, content in self.extracted_content.items():
            summary['files_overview'][file_name] = {
                'sections_count': len(content['sections']),
                'tables_count': len(content['tables']),
                'key_phrases_count': len(content['key_phrases']),
                'technical_terms_count': len(content['technical_terms']),
                'requirements_count': len(content['requirements'])
            }
            
            all_terms.extend(content['technical_terms'])
            all_requirements.extend(content['requirements'])
            all_standards.extend(content['standards_references'])
        
        # 统计频率
        from collections import Counter
        
        term_counter = Counter(all_terms)
        req_counter = Counter(all_requirements)
        std_counter = Counter(all_standards)
        
        summary['common_terms'] = dict(term_counter.most_common(10))
        summary['common_requirements'] = dict(req_counter.most_common(10))
        summary['all_standards'] = list(set(all_standards))
        
        return summary
    
    def export_content_for_writing(self, target_section):
        """导出特定章节的参考内容"""
        section_keywords = {
            '范围': ['范围', '适用', '应用'],
            '术语和定义': ['术语', '定义', '概念'],
            '技术要求': ['要求', '技术', '规定', '标准'],
            '性能指标': ['性能', '指标', '精度', '速度'],
            '测试方法': ['测试', '试验', '验证', '评估']
        }
        
        keywords = section_keywords.get(target_section, [])
        relevant_content = {
            'section': target_section,
            'relevant_sections': [],
            'relevant_requirements': [],
            'relevant_terms': [],
            'suggested_content': []
        }
        
        for file_name, content in self.extracted_content.items():
            # 查找相关章节
            for section in content['sections']:
                if any(keyword in section['title'] for keyword in keywords):
                    relevant_content['relevant_sections'].append({
                        'source': file_name,
                        'title': section['title'],
                        'content': section['content'][:3]  # 只取前3段
                    })
            
            # 查找相关要求
            for req in content['requirements']:
                if any(keyword in req for keyword in keywords):
                    relevant_content['relevant_requirements'].append({
                        'source': file_name,
                        'requirement': req
                    })
            
            # 查找相关术语
            for term in content['technical_terms']:
                if any(keyword in term for keyword in keywords):
                    relevant_content['relevant_terms'].append({
                        'source': file_name,
                        'term': term
                    })
        
        return relevant_content

def main():
    extractor = ContentExtractor()
    
    print("=== 内容提取器 ===\n")
    
    # 要分析的文件列表
    files_to_analyze = [
        ("4-中国电工技术学会标准（模板）.docx", "template"),
        ("4-标准文件的规范化格式范例.docx", "format_example"),
        ("电网设备巡检的电力智能图像编解码总体技术导则_立项资料/电网设备巡检的电力智能图像编解码总体技术导则_标准草案.docx", "reference_standard"),
        ("电网设备巡检的电力智能图像编解码总体技术导则_立项资料/电网设备巡检的电力智能图像编解码总体技术导则_标准预研说明.docx", "research_explanation")
    ]
    
    # 提取内容
    for file_path, content_type in files_to_analyze:
        if Path(file_path).exists():
            print(f"正在分析: {file_path}")
            extractor.extract_from_file(file_path, content_type)
        else:
            print(f"文件不存在: {file_path}")
    
    # 生成摘要
    print("\n生成内容摘要...")
    summary = extractor.generate_content_summary()
    
    print(f"\n=== 内容摘要 ===")
    print(f"分析文件数量: {summary['total_files']}")
    
    print("\n文件概览:")
    for file_name, overview in summary['files_overview'].items():
        print(f"  {file_name}:")
        print(f"    章节数: {overview['sections_count']}")
        print(f"    表格数: {overview['tables_count']}")
        print(f"    关键短语: {overview['key_phrases_count']}")
        print(f"    技术术语: {overview['technical_terms_count']}")
        print(f"    要求语句: {overview['requirements_count']}")
    
    print(f"\n引用标准 ({len(summary['all_standards'])} 个):")
    for std in summary['all_standards'][:10]:  # 显示前10个
        print(f"  - {std}")
    
    # 导出特定章节内容
    print("\n=== 章节内容导出示例 ===")
    sections_to_export = ['范围', '术语和定义', '技术要求']
    
    for section in sections_to_export:
        print(f"\n--- {section} ---")
        relevant = extractor.export_content_for_writing(section)
        
        if relevant['relevant_sections']:
            print("相关章节:")
            for item in relevant['relevant_sections'][:2]:
                print(f"  来源: {item['source']}")
                print(f"  标题: {item['title']}")
                if item['content']:
                    print(f"  内容: {item['content'][0][:100]}...")
        
        if relevant['relevant_requirements']:
            print("相关要求:")
            for item in relevant['relevant_requirements'][:3]:
                print(f"  - {item['requirement'][:100]}...")
    
    print("\n内容提取完成！")
    print("您可以使用提取的内容作为标准撰写的参考。")

if __name__ == "__main__":
    main()
