#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成标准草案
基于模板和参考文档，生成"变电设备不规则视觉缺陷智能检测技术规范"的完整草案
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
import os

def create_standard_draft():
    """创建标准草案文档"""
    
    # 创建新文档
    doc = Document()
    
    # 设置文档样式
    style = doc.styles['Normal']
    style.font.name = '宋体'
    style.font.size = Pt(12)
    style.paragraph_format.line_spacing = 1.5
    
    # 添加标题
    title = doc.add_heading('变电设备不规则视觉缺陷智能检测技术规范', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加副标题
    subtitle = doc.add_paragraph('Technical Specification for Intelligent Detection of Irregular Visual Defects in Substation Equipment')
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle_run = subtitle.runs[0]
    subtitle_run.font.size = Pt(14)
    subtitle_run.font.name = 'Times New Roman'
    
    doc.add_page_break()
    
    # 目次
    add_table_of_contents(doc)
    doc.add_page_break()
    
    # 前言
    add_preface(doc)
    doc.add_page_break()
    
    # 1. 范围
    add_scope_section(doc)
    
    # 2. 规范性引用文件
    add_normative_references(doc)
    
    # 3. 术语和定义
    add_terms_and_definitions(doc)
    
    # 4. 符号、代号和缩略语
    add_symbols_and_abbreviations(doc)
    
    # 5. 总体要求
    add_general_requirements(doc)
    
    # 6. 检测技术要求
    add_detection_technical_requirements(doc)
    
    # 7. 性能指标
    add_performance_indicators(doc)
    
    # 8. 测试方法
    add_test_methods(doc)
    
    # 9. 实施与维护
    add_implementation_and_maintenance(doc)
    
    # 保存文档
    output_file = "变电设备不规则视觉缺陷智能检测技术规范_草案.docx"
    doc.save(output_file)
    print(f"✓ 标准草案已生成: {output_file}")
    
    return output_file

def add_table_of_contents(doc):
    """添加目次"""
    heading = doc.add_heading('目    次', 1)
    heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    toc_items = [
        "前言 ......................................................... I",
        "1  范围 ....................................................... 1",
        "2  规范性引用文件 ............................................. 1", 
        "3  术语和定义 ................................................. 2",
        "4  符号、代号和缩略语 ......................................... 3",
        "5  总体要求 ................................................... 4",
        "6  检测技术要求 ............................................... 5",
        "  6.1  图像采集要求 ........................................... 5",
        "  6.2  图像预处理要求 ......................................... 6",
        "  6.3  缺陷识别算法要求 ....................................... 7",
        "  6.4  结果输出要求 ........................................... 8",
        "7  性能指标 ................................................... 9",
        "  7.1  检测精度指标 ........................................... 9",
        "  7.2  处理速度指标 .......................................... 10",
        "  7.3  可靠性指标 ............................................ 10",
        "8  测试方法 .................................................. 11",
        "  8.1  功能测试 .............................................. 11",
        "  8.2  性能测试 .............................................. 12",
        "  8.3  可靠性测试 ............................................ 13",
        "9  实施与维护 ................................................ 14",
        "  9.1  系统部署 .............................................. 14",
        "  9.2  运行维护 .............................................. 15",
        "  9.3  升级管理 .............................................. 15"
    ]
    
    for item in toc_items:
        p = doc.add_paragraph(item)
        p.paragraph_format.left_indent = Inches(0.5) if item.startswith('  ') else Inches(0)

def add_preface(doc):
    """添加前言"""
    heading = doc.add_heading('前    言', 1)
    heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    preface_content = [
        "本文件按照GB/T 1.1-2020《标准化工作导则 第1部分：标准化文件的结构和起草规则》的规定起草。",
        "",
        "本文件由中国电工技术学会提出。",
        "",
        "本文件由中国电工技术学会归口。",
        "",
        "本文件起草单位：[起草单位名称]。",
        "",
        "本文件主要起草人：[主要起草人姓名]。",
        "",
        "本文件为首次发布。"
    ]
    
    for content in preface_content:
        if content:
            doc.add_paragraph(content)
        else:
            doc.add_paragraph()

def add_scope_section(doc):
    """添加范围章节"""
    doc.add_heading('1  范围', 1)
    
    scope_content = [
        "本文件规定了变电设备不规则视觉缺陷智能检测系统的总体要求、检测技术要求、性能指标、测试方法以及实施与维护要求。",
        "",
        "本文件适用于35kV及以上电压等级变电站内主要电气设备（包括变压器、断路器、隔离开关、避雷器、电容器、电抗器等）的不规则视觉缺陷智能检测系统的设计、开发、测试、部署和运维。",
        "",
        "本文件不适用于：",
        "a) 35kV以下电压等级的电气设备；",
        "b) 非视觉检测方法（如红外检测、超声检测等）；",
        "c) 规则几何形状缺陷的检测。"
    ]
    
    for content in scope_content:
        if content:
            doc.add_paragraph(content)
        else:
            doc.add_paragraph()

def add_normative_references(doc):
    """添加规范性引用文件"""
    doc.add_heading('2  规范性引用文件', 1)
    
    doc.add_paragraph("下列文件中的内容通过文中的规范性引用而构成本文件必不可少的条款。其中，注日期的引用文件，仅该日期对应的版本适用于本文件；不注日期的引用文件，其最新版本（包括所有的修改单）适用于本文件。")
    doc.add_paragraph()
    
    references = [
        "GB/T 2900.1-2008  电工术语 基本术语",
        "GB/T 2900.53-2001  电工术语 电力电子技术",
        "GB/T 14285-2006  继电保护和安全自动装置技术规程",
        "GB/T 50062-2008  电力装置的继电保护和自动装置设计规范",
        "DL/T 596-2005  电力设备预防性试验规程",
        "DL/T 1080.1-2018  电力设备状态监测 第1部分：通用要求",
        "IEEE 802.11  无线局域网标准",
        "ISO/IEC 27001  信息安全管理体系要求"
    ]
    
    for ref in references:
        doc.add_paragraph(ref)

def add_terms_and_definitions(doc):
    """添加术语和定义"""
    doc.add_heading('3  术语和定义', 1)
    
    doc.add_paragraph("GB/T 2900.1-2008界定的以及下列术语和定义适用于本文件。")
    doc.add_paragraph()
    
    terms = [
        ("3.1", "变电设备", "substation equipment", "安装在变电站内用于电能变换、传输、分配和控制的电气设备。"),
        ("3.2", "视觉缺陷", "visual defect", "通过视觉观察或图像分析能够识别的设备外观异常或损伤。"),
        ("3.3", "不规则缺陷", "irregular defect", "形状、大小、位置不规则，难以用几何模型描述的设备缺陷。"),
        ("3.4", "智能检测", "intelligent detection", "基于人工智能技术，自动识别和分析设备缺陷的检测方法。"),
        ("3.5", "深度学习", "deep learning", "基于多层神经网络的机器学习方法。"),
        ("3.6", "目标检测", "object detection", "在图像中定位和识别特定目标的计算机视觉技术。"),
        ("3.7", "图像分割", "image segmentation", "将图像分割成若干个具有相似特征区域的过程。"),
        ("3.8", "特征提取", "feature extraction", "从原始数据中提取有用信息的过程。")
    ]
    
    for number, chinese, english, definition in terms:
        p = doc.add_paragraph()
        p.add_run(f"{number}  ").bold = True
        p.add_run(f"{chinese}  ").bold = True
        p.add_run(f"{english}")
        doc.add_paragraph(definition)
        doc.add_paragraph()

def add_symbols_and_abbreviations(doc):
    """添加符号、代号和缩略语"""
    doc.add_heading('4  符号、代号和缩略语', 1)
    
    doc.add_paragraph("下列符号、代号和缩略语适用于本文件。")
    doc.add_paragraph()
    
    abbreviations = [
        ("AI", "Artificial Intelligence", "人工智能"),
        ("CNN", "Convolutional Neural Network", "卷积神经网络"),
        ("IoU", "Intersection over Union", "交并比"),
        ("mAP", "mean Average Precision", "平均精度均值"),
        ("YOLO", "You Only Look Once", "实时目标检测算法"),
        ("R-CNN", "Region-based CNN", "基于区域的卷积神经网络"),
        ("GPU", "Graphics Processing Unit", "图形处理器"),
        ("API", "Application Programming Interface", "应用程序编程接口")
    ]
    
    for abbr, full_english, chinese in abbreviations:
        p = doc.add_paragraph()
        p.add_run(f"{abbr}  ").bold = True
        p.add_run(f"{full_english}  ")
        p.add_run(chinese)

def add_general_requirements(doc):
    """添加总体要求"""
    doc.add_heading('5  总体要求', 1)
    
    doc.add_heading('5.1  基本原则', 2)
    principles = [
        "a) 系统应具备高精度、高可靠性的缺陷检测能力；",
        "b) 应支持多种类型变电设备的缺陷检测；",
        "c) 应具备实时或准实时的处理能力；",
        "d) 应具备良好的扩展性和兼容性；",
        "e) 应满足电力系统安全性和稳定性要求。"
    ]
    
    for principle in principles:
        doc.add_paragraph(principle)
    
    doc.add_paragraph()
    doc.add_heading('5.2  系统架构', 2)
    doc.add_paragraph("智能检测系统应包括以下主要组成部分：")
    
    components = [
        "a) 图像采集子系统：负责获取设备图像数据；",
        "b) 数据预处理子系统：对原始图像进行预处理；",
        "c) 缺陷检测子系统：基于AI算法进行缺陷识别；",
        "d) 结果分析子系统：对检测结果进行分析和判断；",
        "e) 数据管理子系统：负责数据存储和管理；",
        "f) 用户界面子系统：提供人机交互界面。"
    ]
    
    for component in components:
        doc.add_paragraph(component)

def add_detection_technical_requirements(doc):
    """添加检测技术要求"""
    doc.add_heading('6  检测技术要求', 1)
    
    doc.add_heading('6.1  图像采集要求', 2)
    doc.add_paragraph('6.1.1  图像质量要求')
    
    image_requirements = [
        "a) 图像分辨率应不低于1920×1080像素；",
        "b) 图像色彩深度应不少于24位；",
        "c) 图像压缩格式应支持JPEG、PNG等常用格式；",
        "d) 图像应具备良好的清晰度和对比度。"
    ]
    
    for req in image_requirements:
        doc.add_paragraph(req)
    
    doc.add_paragraph()
    doc.add_paragraph('6.1.2  采集环境要求')
    
    env_requirements = [
        "a) 应适应不同光照条件下的图像采集；",
        "b) 应具备防尘、防水等环境适应能力；",
        "c) 工作温度范围：-40℃～+70℃；",
        "d) 相对湿度：≤95%（无凝露）。"
    ]
    
    for req in env_requirements:
        doc.add_paragraph(req)

def add_performance_indicators(doc):
    """添加性能指标"""
    doc.add_heading('7  性能指标', 1)
    
    doc.add_heading('7.1  检测精度指标', 2)
    
    accuracy_indicators = [
        "a) 缺陷检测准确率应≥95%；",
        "b) 缺陷检测召回率应≥90%；",
        "c) 误检率应≤5%；",
        "d) 漏检率应≤10%。"
    ]
    
    for indicator in accuracy_indicators:
        doc.add_paragraph(indicator)
    
    doc.add_paragraph()
    doc.add_heading('7.2  处理速度指标', 2)
    
    speed_indicators = [
        "a) 单张图像处理时间应≤2秒；",
        "b) 批量图像处理能力应≥100张/小时；",
        "c) 系统响应时间应≤5秒。"
    ]
    
    for indicator in speed_indicators:
        doc.add_paragraph(indicator)

def add_test_methods(doc):
    """添加测试方法"""
    doc.add_heading('8  测试方法', 1)
    
    doc.add_heading('8.1  功能测试', 2)
    doc.add_paragraph("应验证系统各项功能是否符合设计要求，包括：")
    
    function_tests = [
        "a) 图像采集功能测试；",
        "b) 缺陷检测功能测试；",
        "c) 结果输出功能测试；",
        "d) 数据管理功能测试。"
    ]
    
    for test in function_tests:
        doc.add_paragraph(test)
    
    doc.add_paragraph()
    doc.add_heading('8.2  性能测试', 2)
    doc.add_paragraph("应验证系统性能指标是否满足要求，测试方法包括：")
    
    performance_tests = [
        "a) 使用标准测试数据集进行精度测试；",
        "b) 使用计时工具测量处理速度；",
        "c) 进行长时间稳定性测试。"
    ]
    
    for test in performance_tests:
        doc.add_paragraph(test)

def add_implementation_and_maintenance(doc):
    """添加实施与维护"""
    doc.add_heading('9  实施与维护', 1)
    
    doc.add_heading('9.1  系统部署', 2)
    
    deployment_requirements = [
        "a) 应制定详细的部署方案和实施计划；",
        "b) 应进行充分的系统测试和验收；",
        "c) 应提供完整的技术文档和用户手册；",
        "d) 应对相关人员进行培训。"
    ]
    
    for req in deployment_requirements:
        doc.add_paragraph(req)
    
    doc.add_paragraph()
    doc.add_heading('9.2  运行维护', 2)
    
    maintenance_requirements = [
        "a) 应建立定期维护制度；",
        "b) 应监控系统运行状态；",
        "c) 应及时处理系统故障；",
        "d) 应定期更新和优化算法模型。"
    ]
    
    for req in maintenance_requirements:
        doc.add_paragraph(req)

if __name__ == "__main__":
    print("正在生成标准草案...")
    output_file = create_standard_draft()
    print(f"标准草案生成完成！文件保存为：{output_file}")
    print("\n请注意：")
    print("1. 这是一个初步草案，需要根据实际需求进行完善")
    print("2. 建议结合具体的技术规范和行业标准进行修订")
    print("3. 需要专业人员审核和验证技术内容的准确性")
