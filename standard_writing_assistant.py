#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准撰写助手
基于现有.docx文件内容，帮助撰写"变电设备不规则视觉缺陷智能检测技术规范"标准
"""

import os
import sys
from pathlib import Path
from docx import Document
import json

class StandardWritingAssistant:
    def __init__(self):
        self.template_structure = None
        self.reference_content = {}
        self.current_standard_topic = "变电设备不规则视觉缺陷智能检测技术规范"
        
    def load_template_structure(self, template_path="4-中国电工技术学会标准（模板）.docx"):
        """加载标准模板结构"""
        try:
            doc = Document(template_path)
            structure = {
                'sections': [],
                'formatting_rules': [],
                'required_elements': []
            }
            
            for para in doc.paragraphs:
                text = para.text.strip()
                style = para.style.name if para.style else 'Normal'
                
                # 识别章节结构
                if any(keyword in style for keyword in ['章标题', '目次', '前言']):
                    structure['sections'].append({
                        'title': text,
                        'style': style,
                        'level': self._get_section_level(text, style)
                    })
                
                # 识别格式规则
                if any(keyword in text for keyword in ['字体', '格式', '样式', '编号']):
                    structure['formatting_rules'].append(text)
                
                # 识别必需元素
                if any(keyword in text for keyword in ['应', '必须', '规定', '要求']):
                    structure['required_elements'].append(text)
            
            self.template_structure = structure
            print(f"✓ 已加载标准模板结构，包含 {len(structure['sections'])} 个章节")
            return structure
            
        except Exception as e:
            print(f"加载模板失败: {e}")
            return None
    
    def _get_section_level(self, text, style):
        """判断章节层级"""
        if '目次' in text or '前言' in text:
            return 0
        elif text.startswith(('1 ', '2 ', '3 ', '4 ', '5 ', '6 ', '7 ', '8 ', '9 ')):
            return 1
        elif any(text.startswith(f'{i}.{j}') for i in range(1, 10) for j in range(1, 10)):
            return 2
        else:
            return 3
    
    def analyze_reference_documents(self):
        """分析参考文档内容"""
        reference_files = [
            "电网设备巡检的电力智能图像编解码总体技术导则_立项资料/电网设备巡检的电力智能图像编解码总体技术导则_标准草案.docx",
            "电网设备巡检的电力智能图像编解码总体技术导则_立项资料/电网设备巡检的电力智能图像编解码总体技术导则_标准预研说明.docx"
        ]
        
        for file_path in reference_files:
            if os.path.exists(file_path):
                try:
                    doc = Document(file_path)
                    content = {
                        'paragraphs': [],
                        'tables': [],
                        'key_terms': [],
                        'technical_requirements': []
                    }
                    
                    # 提取段落内容
                    for para in doc.paragraphs:
                        text = para.text.strip()
                        if text:
                            content['paragraphs'].append({
                                'text': text,
                                'style': para.style.name if para.style else 'Normal'
                            })
                            
                            # 识别关键术语
                            if any(keyword in text for keyword in ['定义', '术语', '概念']):
                                content['key_terms'].append(text)
                            
                            # 识别技术要求
                            if any(keyword in text for keyword in ['要求', '应', '必须', '规定', '标准']):
                                content['technical_requirements'].append(text)
                    
                    # 提取表格内容
                    for table in doc.tables:
                        table_data = []
                        for row in table.rows:
                            row_data = [cell.text.strip() for cell in row.cells]
                            table_data.append(row_data)
                        content['tables'].append(table_data)
                    
                    self.reference_content[os.path.basename(file_path)] = content
                    print(f"✓ 已分析参考文档: {os.path.basename(file_path)}")
                    
                except Exception as e:
                    print(f"分析文档 {file_path} 失败: {e}")
    
    def generate_standard_outline(self):
        """生成标准大纲"""
        if not self.template_structure:
            print("请先加载模板结构")
            return None
        
        # 基于模板和参考内容生成针对性大纲
        outline = {
            'title': self.current_standard_topic,
            'sections': []
        }
        
        # 标准章节结构
        standard_sections = [
            {'number': '目次', 'title': '目次', 'content': '列出标准的章节结构'},
            {'number': '前言', 'title': '前言', 'content': '说明标准的制定背景、目的和适用范围'},
            {'number': '1', 'title': '范围', 'content': '明确本标准适用于变电设备不规则视觉缺陷智能检测的技术要求'},
            {'number': '2', 'title': '规范性引用文件', 'content': '列出相关的国家标准、行业标准等'},
            {'number': '3', 'title': '术语和定义', 'content': '定义变电设备、视觉缺陷、智能检测等关键术语'},
            {'number': '4', 'title': '符号、代号和缩略语', 'content': '列出标准中使用的符号和缩略语'},
            {'number': '5', 'title': '总体要求', 'content': '变电设备不规则视觉缺陷智能检测系统的总体技术要求'},
            {'number': '6', 'title': '检测技术要求', 'content': '图像采集、预处理、缺陷识别算法等技术要求'},
            {'number': '7', 'title': '性能指标', 'content': '检测精度、召回率、处理速度等性能指标要求'},
            {'number': '8', 'title': '测试方法', 'content': '系统性能测试和验证方法'},
            {'number': '9', 'title': '实施与维护', 'content': '系统部署、运维和升级要求'}
        ]
        
        outline['sections'] = standard_sections
        return outline
    
    def extract_relevant_content(self, section_title):
        """从参考文档中提取相关内容"""
        relevant_content = []
        
        for doc_name, content in self.reference_content.items():
            for para in content['paragraphs']:
                text = para['text']
                # 根据章节标题匹配相关内容
                if self._is_content_relevant(text, section_title):
                    relevant_content.append({
                        'source': doc_name,
                        'text': text,
                        'style': para['style']
                    })
        
        return relevant_content
    
    def _is_content_relevant(self, text, section_title):
        """判断内容是否与章节相关"""
        relevance_keywords = {
            '范围': ['范围', '适用', '应用', '覆盖'],
            '术语和定义': ['定义', '术语', '概念', '含义'],
            '总体要求': ['要求', '原则', '总体', '基本'],
            '检测技术要求': ['检测', '识别', '算法', '技术', '方法'],
            '性能指标': ['性能', '指标', '精度', '准确率', '召回率'],
            '测试方法': ['测试', '验证', '评估', '评价'],
            '实施与维护': ['部署', '实施', '维护', '运维']
        }
        
        keywords = relevance_keywords.get(section_title, [])
        return any(keyword in text for keyword in keywords)
    
    def generate_section_content(self, section_number, section_title):
        """生成特定章节的内容"""
        print(f"\n=== 生成章节: {section_number} {section_title} ===")
        
        # 获取相关参考内容
        relevant_content = self.extract_relevant_content(section_title)
        
        # 生成章节内容框架
        content_framework = self._get_section_framework(section_title)
        
        result = {
            'section_number': section_number,
            'section_title': section_title,
            'framework': content_framework,
            'reference_content': relevant_content[:5],  # 限制显示前5个相关内容
            'suggestions': self._get_writing_suggestions(section_title)
        }
        
        return result
    
    def _get_section_framework(self, section_title):
        """获取章节内容框架"""
        frameworks = {
            '范围': [
                '本标准规定了变电设备不规则视觉缺陷智能检测的...',
                '本标准适用于...',
                '本标准不适用于...'
            ],
            '术语和定义': [
                '变电设备 substation equipment',
                '视觉缺陷 visual defect', 
                '智能检测 intelligent detection',
                '不规则缺陷 irregular defect'
            ],
            '总体要求': [
                '系统应具备自动化检测能力',
                '应支持多种缺陷类型识别',
                '应具备实时处理能力'
            ],
            '检测技术要求': [
                '图像采集要求',
                '图像预处理要求',
                '缺陷识别算法要求',
                '结果输出要求'
            ]
        }
        
        return frameworks.get(section_title, ['待完善的内容框架'])
    
    def _get_writing_suggestions(self, section_title):
        """获取撰写建议"""
        suggestions = {
            '范围': [
                '明确标准的适用对象和范围边界',
                '参考现有电力设备检测标准',
                '考虑不同电压等级的变电设备'
            ],
            '术语和定义': [
                '参考GB/T 2900系列标准中的电力术语',
                '结合人工智能和图像处理领域的标准术语',
                '确保术语定义的准确性和一致性'
            ],
            '检测技术要求': [
                '参考现有图像处理和人工智能相关标准',
                '考虑实际应用场景的技术可行性',
                '制定可量化的技术指标'
            ]
        }
        
        return suggestions.get(section_title, ['根据标准模板和参考文档进行撰写'])

def main():
    assistant = StandardWritingAssistant()
    
    print("=== 变电设备不规则视觉缺陷智能检测技术规范 - 标准撰写助手 ===\n")
    
    # 1. 加载模板结构
    print("1. 加载标准模板...")
    assistant.load_template_structure()
    
    # 2. 分析参考文档
    print("\n2. 分析参考文档...")
    assistant.analyze_reference_documents()
    
    # 3. 生成标准大纲
    print("\n3. 生成标准大纲...")
    outline = assistant.generate_standard_outline()
    
    if outline:
        print(f"\n标准名称: {outline['title']}")
        print("\n标准大纲:")
        for section in outline['sections']:
            print(f"  {section['number']} {section['title']}")
            print(f"    内容要点: {section['content']}")
    
    # 4. 生成具体章节内容示例
    print("\n4. 生成章节内容示例...")
    example_sections = ['范围', '术语和定义', '检测技术要求']
    
    for section_title in example_sections:
        section_content = assistant.generate_section_content('X', section_title)
        
        print(f"\n章节: {section_content['section_title']}")
        print("内容框架:")
        for item in section_content['framework']:
            print(f"  - {item}")
        
        print("撰写建议:")
        for suggestion in section_content['suggestions']:
            print(f"  • {suggestion}")
        
        if section_content['reference_content']:
            print("参考内容:")
            for ref in section_content['reference_content'][:2]:  # 只显示前2个
                print(f"  来源: {ref['source']}")
                print(f"  内容: {ref['text'][:100]}...")

if __name__ == "__main__":
    main()
