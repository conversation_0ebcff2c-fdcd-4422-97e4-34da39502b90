#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
.docx文件读取和处理工具
用于帮助标准文档的撰写和分析
"""

import os
import sys
from pathlib import Path

def install_required_packages():
    """检查必要的Python包"""
    try:
        import docx
        print("python-docx 已安装")
        return True
    except ImportError:
        print("错误：未找到 python-docx 包，请先安装：pip install python-docx")
        return False

def read_docx_file(file_path):
    """
    读取.docx文件内容
    
    Args:
        file_path (str): .docx文件路径
        
    Returns:
        dict: 包含文档内容的字典
    """
    try:
        from docx import Document
        
        doc = Document(file_path)
        
        result = {
            'file_name': os.path.basename(file_path),
            'paragraphs': [],
            'tables': [],
            'headers': [],
            'footers': []
        }
        
        # 读取段落
        for para in doc.paragraphs:
            if para.text.strip():
                result['paragraphs'].append({
                    'text': para.text,
                    'style': para.style.name if para.style else 'Normal'
                })
        
        # 读取表格
        for table_idx, table in enumerate(doc.tables):
            table_data = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    row_data.append(cell.text.strip())
                table_data.append(row_data)
            result['tables'].append({
                'index': table_idx,
                'data': table_data
            })
        
        # 读取页眉页脚
        for section in doc.sections:
            if section.header:
                for para in section.header.paragraphs:
                    if para.text.strip():
                        result['headers'].append(para.text)
            
            if section.footer:
                for para in section.footer.paragraphs:
                    if para.text.strip():
                        result['footers'].append(para.text)
        
        return result
        
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
        return None

def analyze_standard_document(file_path):
    """
    分析标准文档的结构
    
    Args:
        file_path (str): 标准文档路径
    """
    content = read_docx_file(file_path)
    if not content:
        return
    
    print(f"\n=== 分析文档: {content['file_name']} ===")
    
    # 分析标题结构
    print("\n【文档结构】:")
    for para in content['paragraphs']:
        text = para['text']
        style = para['style']
        
        # 识别可能的标题
        if any(keyword in style.lower() for keyword in ['heading', '标题', 'title']) or \
           text.strip().endswith('：') or \
           any(text.startswith(prefix) for prefix in ['第', '1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.']):
            print(f"  {style}: {text[:100]}...")
    
    # 分析表格
    if content['tables']:
        print(f"\n【表格数量】: {len(content['tables'])}")
        for i, table in enumerate(content['tables']):
            print(f"  表格 {i+1}: {len(table['data'])} 行 x {len(table['data'][0]) if table['data'] else 0} 列")
    
    # 统计信息
    total_paragraphs = len(content['paragraphs'])
    total_text = sum(len(para['text']) for para in content['paragraphs'])
    
    print(f"\n【统计信息】:")
    print(f"  段落数量: {total_paragraphs}")
    print(f"  总字符数: {total_text}")
    print(f"  表格数量: {len(content['tables'])}")

def extract_standard_template_structure(template_path):
    """
    提取标准模板的结构，用于指导新标准的撰写
    
    Args:
        template_path (str): 标准模板文件路径
    """
    content = read_docx_file(template_path)
    if not content:
        return None
    
    structure = {
        'sections': [],
        'required_elements': [],
        'format_guidelines': []
    }
    
    current_section = None
    
    for para in content['paragraphs']:
        text = para['text'].strip()
        
        # 识别章节标题
        if text and (text.endswith('：') or 
                    any(text.startswith(prefix) for prefix in ['第', '1', '2', '3', '4', '5', '6', '7', '8', '9']) or
                    '标题' in para['style']):
            current_section = text
            structure['sections'].append(current_section)
        
        # 识别必需元素
        if any(keyword in text for keyword in ['必须', '应当', '规定', '要求']):
            structure['required_elements'].append(text)
        
        # 识别格式指导
        if any(keyword in text for keyword in ['格式', '样式', '字体', '编号']):
            structure['format_guidelines'].append(text)
    
    return structure

def main():
    """主函数"""
    if not install_required_packages():
        return
    
    # 获取当前目录下的所有.docx文件
    current_dir = Path('.')
    docx_files = list(current_dir.glob('*.docx')) + list(current_dir.glob('**/*.docx'))
    
    if not docx_files:
        print("当前目录下没有找到.docx文件")
        return
    
    print("找到以下.docx文件:")
    for i, file_path in enumerate(docx_files, 1):
        print(f"{i}. {file_path}")
    
    print("\n选择操作:")
    print("1. 分析所有文档")
    print("2. 分析特定文档")
    print("3. 提取标准模板结构")

    # 自动选择分析所有文档
    choice = "1"
    print(f"自动选择: {choice}")

    try:
        
        if choice == '1':
            for file_path in docx_files:
                analyze_standard_document(str(file_path))
                print("-" * 50)
        
        elif choice == '2':
            file_num = int(input("请输入文件编号: ")) - 1
            if 0 <= file_num < len(docx_files):
                analyze_standard_document(str(docx_files[file_num]))
            else:
                print("无效的文件编号")
        
        elif choice == '3':
            # 查找标准模板文件
            template_files = [f for f in docx_files if '模板' in str(f) or 'template' in str(f).lower()]
            if template_files:
                print("找到标准模板文件:")
                for i, template in enumerate(template_files, 1):
                    print(f"{i}. {template}")
                
                template_num = int(input("请选择模板文件编号: ")) - 1
                if 0 <= template_num < len(template_files):
                    structure = extract_standard_template_structure(str(template_files[template_num]))
                    if structure:
                        print("\n=== 标准模板结构 ===")
                        print("\n【章节结构】:")
                        for section in structure['sections']:
                            print(f"  - {section}")
                        
                        print("\n【必需元素】:")
                        for element in structure['required_elements'][:10]:  # 显示前10个
                            print(f"  - {element[:100]}...")
                        
                        print("\n【格式指导】:")
                        for guideline in structure['format_guidelines'][:10]:  # 显示前10个
                            print(f"  - {guideline[:100]}...")
            else:
                print("未找到标准模板文件")
        
        else:
            print("无效的选择")
    
    except (ValueError, KeyboardInterrupt):
        print("\n操作已取消")

if __name__ == "__main__":
    main()
